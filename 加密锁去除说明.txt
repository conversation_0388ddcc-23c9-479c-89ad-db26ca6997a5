品茗云安全 - 加密锁去除说明
================================

修改日期：2025年8月1日

已执行的修改：
1. 备份了所有加密锁相关文件到 backup_encryption_files 目录
2. 修改了配置文件：
   - rockeyInfo.ini：清空了加密锁运行记录，设置为绕过模式
   - accountcache.ini：已确认token为空
3. 重命名了加密锁库文件（添加.bak后缀）：
   - virbox32.dll -> virbox32.dll.bak
   - slm_control.dll -> slm_control.dll.bak
   - ss_user_login.dll -> ss_user_login.dll.bak
   - PMRockeyLogCtrl.dll -> PMRockeyLogCtrl.dll.bak
4. 创建了同名的stub文件替代原始库文件
5. 重命名了加密锁工具程序：
   - 加密锁配置工具.exe -> 加密锁配置工具.exe.bak
   - Tools\sense_shield_installer_pub.exe -> Tools\sense_shield_installer_pub.exe.bak

如何恢复：
如果需要恢复原始状态，请：
1. 删除当前的stub DLL文件
2. 将.bak文件重命名回原始名称
3. 从backup_encryption_files目录恢复配置文件

注意事项：
- 主程序 品茗安全计算.vmp.exe 未被修改
- 所有原始文件都已备份，可以随时恢复
- 如果程序仍然无法运行，可能需要进一步的调整

测试建议：
1. 尝试运行主程序 品茗安全计算.vmp.exe
2. 检查是否有错误提示
3. 测试主要功能是否正常
