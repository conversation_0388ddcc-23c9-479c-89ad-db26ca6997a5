# Set internal debugging
log4d.configDebug=false

# Global logging level
log4d.threshold=warn


# Set logger factory
log4d.loggerFactory=TLogDefaultLoggerFactory

# Set root level
log4d.rootLogger=all,ODS

# Establish logger hierarchy
log4d.logger.pmkj=inherited,pmkj

# Create root appender
log4d.appender.ODS=TLogODSAppender
log4d.appender.ODS.layout=TLogSimpleLayout

# Create a file appender
log4d.appender.pmkj=TLogFileAppender
log4d.appender.pmkj.append=true
log4d.appender.pmkj.fileName=LogFiles
log4d.appender.pmkj.errorHandler=TLogOnlyOnceErrorHandler
log4d.appender.pmkj.layout=TLogPatternLayout
log4d.appender.pmkj.layout.dateFormat=YYYY-MM-DD HH:MM:SS.ZZZ
log4d.appender.pmkj.layout.pattern=%d [%-5p] %m%n