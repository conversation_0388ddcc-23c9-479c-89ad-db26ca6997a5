2025-03-11 17:49:22.869 [error] Exception in TPmObj.LoadProperty(ClassName:TElecScheme,PropName:Tag,Prefix:ElcP.),Exception:Access violation at address 2E6268F4 in module 'pmsd.dll'. Read of address 00000000
2025-03-11 17:49:22.870 [error] Exception in TPmObj.LoadProperty(ClassName:TElecScheme,PropName:Caption,Prefix:ElcP.),Exception:Access violation at address 2E6268F4 in module 'pmsd.dll'. Read of address 00000000
2025-03-11 17:49:22.871 [error] Exception in TPmObj.LoadProperty(ClassName:TElecScheme,PropName:TypeId,Prefix:ElcP.),Exception:Access violation at address 2E6268F4 in module 'pmsd.dll'. Read of address 00000000
2025-03-11 17:49:22.872 [error] Exception in TPmObj.LoadProperty(ClassName:TElecScheme,PropName:index,Prefix:ElcP.),Exception:Access violation at address 2E6268F4 in module 'pmsd.dll'. Read of address 00000000
2025-03-11 17:49:22.872 [error] Exception in TPmObj.LoadProperty(ClassName:TElecScheme,PropName:P_index,Prefix:ElcP.),Exception:Access violation at address 2E6268F4 in module 'pmsd.dll'. Read of address 00000000
2025-03-11 17:49:22.873 [error] Exception in TPmObj.LoadProperty(ClassName:TElecScheme,PropName:deleted,Prefix:ElcP.),Exception:Access violation at address 2E6268F4 in module 'pmsd.dll'. Read of address 00000000
2025-03-11 17:49:22.875 [error] Exception in TPmObj.LoadProperty(ClassName:TElecScheme,PropName:GXName,Prefix:ElcP.),Exception:Access violation at address 2E6268F4 in module 'pmsd.dll'. Read of address 00000000
2025-03-11 17:49:22.876 [error] Exception in TPmObj.LoadProperty(ClassName:TElecScheme,PropName:Kx,Prefix:ElcP.),Exception:Access violation at address 2E6268F4 in module 'pmsd.dll'. Read of address 00000000
2025-03-11 17:49:22.876 [error] Exception in TPmObj.LoadProperty(ClassName:TElecScheme,PropName:Co,Prefix:ElcP.),Exception:Access violation at address 2E6268F4 in module 'pmsd.dll'. Read of address 00000000
2025-03-11 17:49:22.877 [error] Exception in TPmObj.LoadProperty(ClassName:TElecScheme,PropName:GxLen,Prefix:ElcP.),Exception:Access violation at address 2E6268F4 in module 'pmsd.dll'. Read of address 00000000
2025-03-11 17:49:24.625 [warn ] Invalid data(Machine) when Invoke PmStrToFloat
2025-03-11 17:49:24.625 [warn ] Invalid data(Machine) when Invoke PmStrToFloat
2025-03-11 17:49:24.706 [warn ] Invalid data(Machine) when Invoke PmStrToFloat
2025-03-11 17:49:24.706 [warn ] Invalid data(Machine) when Invoke PmStrToFloat
2025-03-11 17:49:24.706 [warn ] Invalid data(Machine) when Invoke PmStrToFloat
2025-03-11 17:49:24.706 [warn ] Invalid data(Machine) when Invoke PmStrToFloat
2025-03-11 17:49:24.706 [warn ] Invalid data(Machine) when Invoke PmStrToFloat
2025-03-11 17:49:24.732 [warn ] Invalid data(Machine) when Invoke PmStrToFloat
2025-03-11 17:49:24.732 [warn ] Invalid data(Machine) when Invoke PmStrToFloat
2025-03-11 17:49:24.732 [warn ] Invalid data(Machine) when Invoke PmStrToFloat
2025-03-11 17:49:24.732 [warn ] Invalid data(Machine) when Invoke PmStrToFloat
2025-03-11 17:49:24.732 [warn ] Invalid data(Machine) when Invoke PmStrToFloat
2025-03-11 17:49:24.733 [warn ] Invalid data(Machine) when Invoke PmStrToFloat
2025-03-11 17:49:26.419 [warn ] Invalid data(Lamp) when Invoke PmStrToFloat
2025-03-11 17:49:27.739 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.739 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.740 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.740 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.740 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.740 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.740 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.740 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.740 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.740 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.740 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.740 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.740 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.740 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.741 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.741 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.741 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.741 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.741 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.741 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.741 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.741 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.741 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.741 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.741 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.741 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.741 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.742 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.742 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.742 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.742 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.742 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.742 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.742 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.742 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.742 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.743 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.744 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.744 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.744 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.744 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.744 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.744 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.744 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.744 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.744 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.744 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.744 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.744 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.745 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.745 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.745 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.745 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.745 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.745 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.745 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.745 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.745 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.745 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.745 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.745 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.745 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.746 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.746 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.746 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.746 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.746 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.746 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.746 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.746 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.746 [warn ] Invalid data() when Invoke PmStrToFloat
2025-03-11 17:49:27.746 [warn ] Invalid data() when Invoke PmStrToFloat
